"""
Session management routes.
"""
from typing import Dict, Any
from fastapi import APIRouter, HTTPException, status, Header

router = APIRouter()
from app.core.logging import get_logger
from app.services.session_service import session_service
from app.services.intent_service import intent_service
from app.services.gemini_service import gemini_service

router = APIRouter()
logger = get_logger(__name__)

@router.post("/chat", response_model=schemas.ChatResponse)
async def chat_with_session(
    message: schemas.MessageRequest,
    session_id: str = Header(..., description="Session identifier"),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Handle chat with session-based context.
    """
    try:
        logger.info(f"Processing chat for session: {session_id}")
        
        # Process the message with the session service
        response = session_service.process_message(
            session_id=session_id,
            user_id=current_user.id,
            message=message.message
        )
        
        return response
        
    except Exception as e:
        logger.error(f"Error processing chat message: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while processing your message"
        )

@router.get("/{session_id}", response_model=schemas.Session)
def get_session(
    session_id: str,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get session by ID.
    """
    session = session_service.get_session(session_id, user_id=current_user.id)
    if not session:
        raise HTTPException(status_code=404, detail="Session not found")
    return session

@router.get("/", response_model=List[Session])
def list_sessions(
    skip: int = 0,
    limit: int = 100,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve user's sessions.
    """
    sessions = session_service.get_user_sessions(
        user_id=current_user.id,
        skip=skip,
        limit=limit
    )
    return sessions

@router.delete("/{session_id}", response_model=schemas.Msg)
def delete_session(
    session_id: str,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Delete a session.
    """
    session_service.delete_session(session_id, user_id=current_user.id)
    return {"msg": "Session deleted successfully"}

@router.post("/{session_id}/reset", response_model=Session)
def reset_session(
    session_id: str,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Reset a session, clearing its history.
    """
    session = session_service.reset_session(session_id, user_id=current_user.id)
    if not session:
        raise HTTPException(status_code=404, detail="Session not found")
    return session
